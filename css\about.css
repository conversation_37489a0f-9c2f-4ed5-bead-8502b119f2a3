/* About Page Specific Styles */

/* About Hero Section */
.about-hero {
    background: linear-gradient(135deg, var(--deep-ocean), var(--ocean-blue));
    padding: 8rem 0 6rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,50 Q50,20 80,50 Q50,80 20,50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
    animation: wave 15s ease-in-out infinite;
}

.about-hero-content {
    position: relative;
    z-index: 2;
}

.about-hero h1 {
    font-size: 3.5rem;
    color: white;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.about-hero p {
    font-size: 1.3rem;
    color: var(--pearl-white);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Mission Section */
.mission-section {
    padding: 6rem 0;
    background: var(--pearl-white);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 2rem;
    font-weight: 700;
}

.mission-text p {
    color: #666;
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.mission-highlights {
    list-style: none;
    padding: 0;
}

.mission-highlights li {
    background: white;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 15px;
    border-left: 5px solid var(--coral-pink);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.mission-highlights li:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.mission-visual {
    background: linear-gradient(135deg, var(--sea-foam), var(--soft-blue));
    padding: 3rem;
    border-radius: 25px;
    text-align: center;
    position: relative;
}

.mission-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    animation: float 3s ease-in-out infinite;
}

/* Team Section */
.team-section {
    padding: 6rem 0;
    background: white;
}

.team-section h2 {
    text-align: center;
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 4rem;
    font-weight: 700;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    max-width: 800px;
    margin: 0 auto;
}

.team-card {
    background: linear-gradient(135deg, var(--soft-blue), white);
    padding: 3rem;
    border-radius: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.team-card:hover {
    transform: translateY(-15px);
    border-color: var(--tropical-teal);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.team-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    border-radius: 50%;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    border: 5px solid white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.team-card h3 {
    font-size: 1.8rem;
    color: var(--deep-ocean);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.team-role {
    color: var(--ocean-blue);
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.team-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

/* Values Carousel Section */
.values-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--sea-foam), var(--pearl-white));
}

.values-section h2 {
    text-align: center;
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 4rem;
    font-weight: 700;
}

.values-carousel-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.values-carousel {
    overflow: hidden;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.values-track {
    display: flex;
    transition: transform 0.6s ease-in-out;
    width: 400%; /* 4 cards × 100% */
}

.value-card {
    flex: 0 0 25%; /* Each card takes 25% of track width */
    background: linear-gradient(135deg, var(--soft-blue), white);
    padding: 3rem;
    text-align: center;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0.7;
    transform: scale(0.95);
    transition: all 0.6s ease;
}

.value-card.active {
    opacity: 1;
    transform: scale(1);
}

.value-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--coral-pink), var(--warm-sand));
    padding: 1.5rem;
    border-radius: 50%;
    display: inline-block;
    color: white;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-card h3 {
    font-size: 1.8rem;
    color: var(--deep-ocean);
    margin-bottom: 1rem;
    font-weight: 600;
}

.value-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* Carousel Navigation */
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    z-index: 10;
}

.carousel-prev {
    left: -25px;
}

.carousel-next {
    right: -25px;
}

.carousel-arrow:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
    background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
}

.carousel-arrow:active {
    transform: translateY(-50%) scale(0.95);
}

/* Carousel Indicators */
.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    margin-top: 2rem;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(37, 99, 235, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: var(--ocean-blue);
    transform: scale(1.3);
}

.indicator:hover {
    background: var(--tropical-teal);
    transform: scale(1.1);
}

/* Values Section */
.values-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--sea-foam), var(--pearl-white));
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.values-section h2 {
    text-align: center;
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 4rem;
    font-weight: 700;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
}

.value-card {
    background: white !important;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.value-card:hover {
    transform: translateY(-10px);
    border-color: var(--coral-pink);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.value-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--coral-pink), var(--warm-sand));
    padding: 1.5rem;
    border-radius: 50%;
    display: inline-block;
    color: white;
}

.value-card h3 {
    font-size: 1.5rem;
    color: var(--deep-ocean);
    margin-bottom: 1rem;
    font-weight: 600;
}

.value-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: white;
}

.features-section h2 {
    text-align: center;
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 4rem;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--coral-pink);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--coral-pink), var(--warm-sand));
    padding: 1.5rem;
    border-radius: 50%;
    display: inline-block;
    color: white;
}

.feature-card h3 {
    font-size: 1.5rem;
    color: var(--deep-ocean);
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

/* Contact Section */
.contact-section {
    padding: 6rem 0;
    background: white;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.8rem;
    color: var(--deep-ocean);
    margin-bottom: 4rem;
    font-weight: 700;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-card {
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.contact-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 1.5rem;
    border-radius: 50%;
    display: inline-block;
}

.contact-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-card p {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.contact-card a {
    color: white;
    text-decoration: none;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.contact-card a:hover {
    border-bottom-color: white;
}



/* Animations */
@keyframes wave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-20px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .about-hero h1 {
        font-size: 2.5rem;
    }

    .about-hero p {
        font-size: 1.1rem;
    }

    .mission-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }



    section h2 {
        font-size: 2.2rem;
    }
}
